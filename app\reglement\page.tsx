"use client"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"

export default function ReglementPage() {
  return (
    <div className="dark">
      <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Règlement</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 space-y-6 p-6 min-h-screen bg-gradient-to-br from-slate-950 via-blue-950 to-slate-950 relative overflow-hidden">
            {/* Decorative Images */}
            <motion.div
              initial={{ opacity: 0, x: -100, rotate: -10 }}
              animate={{ opacity: 0.06, x: 0, rotate: 0 }}
              transition={{ duration: 1.2, delay: 0.3 }}
              className="absolute top-16 left-4 w-20 h-20 z-0"
            >
              <img src="/image1.png" alt="" className="w-full h-full object-contain opacity-15" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: -100, rotate: 15 }}
              animate={{ opacity: 0.08, y: 0, rotate: 0 }}
              transition={{ duration: 1.2, delay: 0.6 }}
              className="absolute top-40 right-6 w-24 h-24 z-0"
            >
              <img src="/image2.png" alt="" className="w-full h-full object-contain opacity-20" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 100, rotate: -20 }}
              animate={{ opacity: 0.1, x: 0, rotate: 0 }}
              transition={{ duration: 1.2, delay: 0.9 }}
              className="absolute bottom-20 right-8 w-28 h-28 z-0"
            >
              <img src="/image3.png" alt="" className="w-full h-full object-contain opacity-25" />
            </motion.div>

            {/* Section Règles Générales */}
            <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/10 to-blue-500/5 relative z-10">
              <CardHeader>
                <Badge className="w-fit mb-2 bg-blue-500/20 text-blue-300 border-blue-500/30">Règles Générales</Badge>
                <CardTitle>Règlement du Royaume des Tempêtes</CardTitle>
                <CardDescription>Les règles fondamentales à respecter pour une expérience de jeu agréable</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="font-semibold text-lg mb-2 text-blue-400">1. Respect et Communication</h3>
                  <ul className="list-disc pl-6 space-y-2 text-foreground">
                    <li>Respectez tous les joueurs et membres du staff</li>
                    <li>Pas de langage offensant, raciste ou discriminatoire</li>
                    <li>Évitez le spam et les messages provocateurs</li>
                    <li>Communication en français uniquement dans le chat général</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold text-lg mb-2 text-blue-400">2. Sécurité et Triche</h3>
                  <ul className="list-disc pl-6 space-y-2 text-foreground">
                    <li>Interdiction d'utiliser des mods non autorisés</li>
                    <li>L'exploitation de bugs est strictement interdite</li>
                    <li>Pas de partage de compte ou de vente d'items contre de l'argent réel</li>
                    <li>Les logiciels de triche entraînent un bannissement permanent</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Section Règles Spécifiques */}
            <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/10 to-purple-500/5 relative z-10">
              <CardHeader>
                <Badge className="w-fit mb-2 bg-purple-500/20 text-purple-300 border-purple-500/30">Règles Spécifiques</Badge>
                <CardTitle>Règles par Zone</CardTitle>
                <CardDescription>Règles spécifiques selon les différentes zones du jeu</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="font-semibold text-lg mb-2 text-purple-400">Zones PvP</h3>
                  <ul className="list-disc pl-6 space-y-2 text-foreground">
                    <li>Le PvP n'est autorisé que dans les zones désignées</li>
                    <li>Pas de camping des zones de spawn</li>
                    <li>Les combats doivent rester fair-play</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold text-lg mb-2 text-purple-400">Zones Safe</h3>
                  <ul className="list-disc pl-6 space-y-2 text-foreground">
                    <li>Le PvP est désactivé dans les villes principales</li>
                    <li>Le commerce est autorisé uniquement dans les zones marchandes</li>
                    <li>Respectez la propriété des autres joueurs</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Section Sanctions */}
            <Card className="border-red-500/20 bg-gradient-to-br from-red-500/10 to-pink-500/5 relative z-10">
              <CardHeader>
                <Badge className="w-fit mb-2 bg-red-500/20 text-red-300 border-red-500/30">Sanctions</Badge>
                <CardTitle>Système de Sanctions</CardTitle>
                <CardDescription>Les conséquences en cas de non-respect du règlement</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-2 border border-red-500/20 rounded">
                    <span className="text-foreground">Avertissement</span>
                    <Badge className="bg-yellow-500/20 text-yellow-300">1ère infraction</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 border border-red-500/20 rounded">
                    <span className="text-foreground">Mute temporaire</span>
                    <Badge className="bg-orange-500/20 text-orange-300">2ème infraction</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 border border-red-500/20 rounded">
                    <span className="text-foreground">Ban temporaire</span>
                    <Badge className="bg-red-500/20 text-red-300">3ème infraction</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 border border-red-500/20 rounded">
                    <span className="text-foreground">Ban permanent</span>
                    <Badge className="bg-red-900/20 text-red-300">Infraction grave</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
      </main>
    </div>
  )
}