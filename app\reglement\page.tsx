"use client"

import { useState } from "react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  ChevronDown,
  ChevronRight,
  Search,
  Shield,
  Users,
  Sword,
  Home,
  AlertTriangle,
  Scale,
  BookOpen,
  Link
} from "lucide-react"
import { motion } from "framer-motion"

// Données des règles organisées par catégories
const rulesData = [
  {
    id: "general",
    title: "Règles Générales",
    icon: BookO<PERSON>,
    color: "blue",
    description: "Règles fondamentales pour tous les joueurs",
    rules: [
      {
        title: "1. Respect et Communication",
        content: [
          "Respectez tous les joueurs et membres du staff en toutes circonstances",
          "Aucun langage offensant, raciste, homophobe ou discriminatoire ne sera toléré",
          "Évitez le spam, les messages provocateurs et les discussions hors-sujet",
          "La communication en français est obligatoire dans le chat général",
          "Les discussions privées peuvent se faire dans la langue de votre choix"
        ]
      },
      {
        title: "2. Sécurité et Intégrité du Jeu",
        content: [
          "L'utilisation de mods non autorisés est strictement interdite",
          "L'exploitation de bugs ou de failles est passible de sanctions",
          "Le partage de compte ou la vente d'items contre de l'argent réel est interdit",
          "Les logiciels de triche entraînent un bannissement permanent immédiat",
          "Signalez tout comportement suspect au staff"
        ]
      },
      {
        title: "3. Comportement en Jeu",
        content: [
          "Maintenez un comportement mature et respectueux",
          "Aidez les nouveaux joueurs à s'intégrer dans la communauté",
          "Respectez les décisions du staff et suivez leurs instructions",
          "En cas de conflit, contactez un modérateur plutôt que de régler cela vous-même",
          "Participez activement à créer une atmosphère positive"
        ]
      }
    ]
  },
  {
    id: "rp",
    title: "Règles de Roleplay",
    icon: Users,
    color: "purple",
    description: "Directives pour une expérience RP immersive",
    rules: [
      {
        title: "1. Fondamentaux du RP",
        content: [
          "Restez dans votre personnage (IC) en permanence dans les zones RP",
          "Séparez clairement les connaissances de votre personnage (IC) et les vôtres (OOC)",
          "Développez un background cohérent pour votre personnage",
          "Respectez l'univers et le lore du Royaume des Tempêtes",
          "Évitez les anachronismes et les références au monde moderne"
        ]
      },
      {
        title: "2. Interactions RP",
        content: [
          "Privilégiez toujours le roleplay aux mécaniques de jeu",
          "Acceptez les conséquences des actions de votre personnage",
          "Ne forcez pas d'actions sur les autres personnages sans leur accord",
          "Utilisez /me et /do pour décrire vos actions et l'environnement",
          "Respectez le temps de réaction des autres joueurs"
        ]
      },
      {
        title: "3. Conflits et Combat RP",
        content: [
          "Tentez toujours de résoudre les conflits par le roleplay avant le combat",
          "Respectez les règles de combat RP et les jets de dés si nécessaires",
          "Pas de PowerGaming (actions irréalistes) ou de MetaGaming",
          "La mort RP doit être acceptée si elle est justifiée narrativement",
          "En cas de désaccord, faites appel à un modérateur RP"
        ]
      }
    ]
  },
  {
    id: "pvp",
    title: "Règles PvP",
    icon: Sword,
    color: "red",
    description: "Règles pour les combats entre joueurs",
    rules: [
      {
        title: "1. Zones de Combat",
        content: [
          "Le PvP n'est autorisé que dans les zones spécifiquement désignées",
          "Respectez les limites des zones PvP et Safe",
          "Aucun camping des zones de spawn ou de téléportation",
          "Les duels peuvent avoir lieu dans les arènes prévues à cet effet",
          "Vérifiez toujours les règles spécifiques de chaque zone"
        ]
      },
      {
        title: "2. Fair-Play en Combat",
        content: [
          "Tous les combats doivent rester équitables et sportifs",
          "Pas d'exploitation de bugs ou de mécaniques non intentionnelles",
          "Respectez les accords de duel (règles, mises, conditions)",
          "Aidez les joueurs moins expérimentés à s'améliorer",
          "Acceptez vos défaites avec élégance"
        ]
      },
      {
        title: "3. Guildes et Alliances",
        content: [
          "Les guerres de guildes doivent être déclarées officiellement",
          "Respectez les traités et accords entre guildes",
          "Pas d'attaques sur les membres non-combattants",
          "Les alliances temporaires sont autorisées mais doivent être transparentes",
          "Consultez les règles spécifiques aux événements PvP"
        ]
      }
    ]
  },
  {
    id: "zones",
    title: "Règles par Zones",
    icon: Home,
    color: "green",
    description: "Règles spécifiques selon les différentes zones",
    rules: [
      {
        title: "1. Zones Safe (Villes Principales)",
        content: [
          "Le PvP est complètement désactivé dans toutes les villes principales",
          "Le commerce et les échanges sont encouragés dans les zones marchandes",
          "Respectez la propriété privée et les constructions des autres joueurs",
          "Maintenez un comportement civilisé et respectueux",
          "Les événements communautaires ont priorité sur les activités individuelles"
        ]
      },
      {
        title: "2. Zones Neutres",
        content: [
          "Zones de passage où le PvP est limité selon des règles spécifiques",
          "Respectez les caravanes et les convois commerciaux",
          "Les ressources communes doivent être partagées équitablement",
          "Aidez les joueurs en difficulté si possible",
          "Signalez tout comportement antisocial aux modérateurs"
        ]
      },
      {
        title: "3. Zones Dangereuses",
        content: [
          "Le PvP est autorisé mais doit rester dans l'esprit du jeu",
          "Les ressources rares peuvent être disputées",
          "Formez des groupes pour explorer ces zones en sécurité",
          "Respectez les règles de butin et de pillage établies",
          "Attention aux créatures hostiles et aux pièges environnementaux"
        ]
      }
    ]
  },
  {
    id: "sanctions",
    title: "Système de Sanctions",
    icon: Scale,
    color: "red",
    description: "Conséquences en cas de non-respect du règlement",
    rules: [
      {
        title: "1. Échelle des Sanctions",
        content: [
          "Avertissement verbal : Première infraction mineure",
          "Avertissement écrit : Récidive ou infraction modérée",
          "Mute temporaire : 1h à 24h selon la gravité",
          "Exclusion temporaire : 1 jour à 1 mois selon l'infraction",
          "Bannissement permanent : Infractions graves ou récidives multiples"
        ]
      },
      {
        title: "2. Infractions Graves (Bannissement Immédiat)",
        content: [
          "Utilisation de logiciels de triche ou de hacks",
          "Harcèlement répété ou menaces envers d'autres joueurs",
          "Discrimination, racisme ou propos haineux",
          "Tentative de piratage ou d'attaque du serveur",
          "Vente d'items ou de comptes contre de l'argent réel"
        ]
      },
      {
        title: "3. Procédure d'Appel",
        content: [
          "Tout joueur sanctionné peut faire appel de sa sanction",
          "Les appels doivent être envoyés par ticket dans les 7 jours",
          "Fournissez des preuves et explications détaillées",
          "Un staff différent examinera votre cas",
          "La décision d'appel est définitive et ne peut être contestée"
        ]
      }
    ]
  }
]

export default function ReglementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [openSections, setOpenSections] = useState<string[]>([])

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }

  const filteredRules = rulesData.map(category => ({
    ...category,
    rules: category.rules.filter(rule =>
      rule.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.content.some(content => content.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  })).filter(category => category.rules.length > 0)

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "border-blue-500/20 bg-blue-500/5",
      purple: "border-purple-500/20 bg-purple-500/5",
      red: "border-red-500/20 bg-red-500/5",
      green: "border-green-500/20 bg-green-500/5"
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  const getIconColor = (color: string) => {
    const colorMap = {
      blue: "text-blue-400",
      purple: "text-purple-400",
      red: "text-red-400",
      green: "text-green-400"
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur supports-[backdrop-filter]:bg-base-100/80 px-4 shadow-sm relative z-10">
        <SidebarTrigger className="-ml-1 hover:bg-primary/10 transition-colors" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-base-300/50" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/" className="text-base-content/60 hover:text-base-content transition-colors">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-base-content/40" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Règlement</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 p-6 min-h-screen relative overflow-hidden">
        {/* Consistent Background Image */}
        <motion.div
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 0.15, scale: 1 }}
          transition={{ duration: 2 }}
          className="absolute inset-0 z-0"
        >
          <img src="/image2.png" alt="" className="w-full h-full object-cover opacity-15 mix-blend-soft-light" />
        </motion.div>

        <div className="relative z-10 max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-4"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <Scale className="h-8 w-8 text-primary" />
              <h1 className="text-4xl font-bold text-base-content">Règlement du Royaume</h1>
            </div>
            <p className="text-lg text-base-content/70 max-w-3xl mx-auto">
              Consultez toutes les règles organisées par catégories. Cliquez sur une section pour voir les détails.
            </p>
          </motion.div>

          {/* Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="max-w-md mx-auto"
          >
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-base-content/50" />
              <Input
                placeholder="Rechercher dans les règles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-base-100/80 border-base-300 focus:border-primary"
              />
            </div>
          </motion.div>

          {/* Rules Categories */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="space-y-4"
          >
            {filteredRules.map((category, categoryIndex) => {
              const IconComponent = category.icon
              const isOpen = openSections.includes(category.id)

              return (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
                >
                  <Collapsible open={isOpen} onOpenChange={() => toggleSection(category.id)}>
                    <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300">
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-base-200/50 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg ${getColorClasses(category.color)}`}>
                                <IconComponent className={`h-5 w-5 ${getIconColor(category.color)}`} />
                              </div>
                              <div className="text-left">
                                <CardTitle className="text-base-content text-xl">
                                  {category.title}
                                </CardTitle>
                                <CardDescription className="text-base-content/70">
                                  {category.description}
                                </CardDescription>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge className="bg-primary/20 text-primary border-primary/30">
                                {category.rules.length} règle{category.rules.length > 1 ? 's' : ''}
                              </Badge>
                              {isOpen ? (
                                <ChevronDown className="h-5 w-5 text-base-content/60" />
                              ) : (
                                <ChevronRight className="h-5 w-5 text-base-content/60" />
                              )}
                            </div>
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>

                      <CollapsibleContent>
                        <CardContent className="pt-0">
                          <div className="space-y-6">
                            {category.rules.map((rule, ruleIndex) => (
                              <motion.div
                                key={ruleIndex}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.4, delay: ruleIndex * 0.1 }}
                                className="border-l-4 border-primary/30 pl-6 py-4 bg-primary/5 rounded-r-lg"
                              >
                                <h3 className="text-lg font-semibold text-base-content mb-3">
                                  {rule.title}
                                </h3>
                                <ul className="space-y-2">
                                  {rule.content.map((item, itemIndex) => (
                                    <li key={itemIndex} className="flex items-start gap-2 text-base-content/80">
                                      <div className="w-1.5 h-1.5 rounded-full bg-primary/60 mt-2 flex-shrink-0" />
                                      <span className="leading-relaxed">{item}</span>
                                    </li>
                                  ))}
                                </ul>
                              </motion.div>
                            ))}
                          </div>
                        </CardContent>
                      </CollapsibleContent>
                    </Card>
                  </Collapsible>
                </motion.div>
              )
            })}
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-12"
          >
            <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg">
              <CardHeader>
                <CardTitle className="text-base-content flex items-center gap-2">
                  <Link className="h-5 w-5" />
                  Liens Rapides
                </CardTitle>
                <CardDescription className="text-base-content/70">
                  Accès direct aux sections importantes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                  {rulesData.map((category) => {
                    const IconComponent = category.icon
                    return (
                      <Button
                        key={category.id}
                        variant="outline"
                        onClick={() => toggleSection(category.id)}
                        className="h-auto p-3 flex flex-col items-center gap-2 border-base-300 hover:bg-base-200"
                      >
                        <IconComponent className={`h-4 w-4 ${getIconColor(category.color)}`} />
                        <span className="text-xs text-center">{category.title}</span>
                      </Button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* No Results Message */}
          {searchTerm && filteredRules.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <AlertTriangle className="h-12 w-12 text-base-content/40 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-base-content mb-2">Aucun résultat trouvé</h3>
              <p className="text-base-content/60">
                Essayez avec d'autres mots-clés ou parcourez les catégories ci-dessus.
              </p>
            </motion.div>
          )}
        </div>
      </main>
    </div>
  )
}