"use client"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Crown, Swords, Users, Coins, Activity } from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { motion } from "framer-motion"

export default function ClassementsPage() {
  return (
    <div className="dark">
      <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Classements</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 space-y-6 p-6 min-h-screen bg-gradient-to-br from-slate-950 via-blue-950 to-slate-950 relative overflow-hidden">
            {/* Full Screen Background Images */}
            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.04, scale: 1 }}
              transition={{ duration: 3, delay: 0.3 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image1.png" alt="" className="w-full h-full object-cover opacity-12" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.03, scale: 1 }}
              transition={{ duration: 3, delay: 0.8 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image2.png" alt="" className="w-full h-full object-cover opacity-10 mix-blend-overlay" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.05, scale: 1 }}
              transition={{ duration: 3, delay: 1.3 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image3.png" alt="" className="w-full h-full object-cover opacity-15 mix-blend-soft-light" />
            </motion.div>

            {/* Top Joueurs */}
            <Card className="border-yellow-500/20 bg-gradient-to-br from-yellow-500/10 to-orange-500/5 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Crown className="h-6 w-6 text-yellow-400" />
                  <div>
                    <CardTitle>Top Joueurs</CardTitle>
                    <CardDescription>Les meilleurs joueurs du royaume</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">Rang</TableHead>
                      <TableHead>Joueur</TableHead>
                      <TableHead>Niveau</TableHead>
                      <TableHead>Points</TableHead>
                      <TableHead className="text-right">Récompense</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell><Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">1</Badge></TableCell>
                      <TableCell className="font-medium">DragonSlayer</TableCell>
                      <TableCell>100</TableCell>
                      <TableCell>15,420</TableCell>
                      <TableCell className="text-right">100,000 <span className="text-yellow-400">★</span></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><Badge className="bg-slate-300/20 text-slate-300 border-slate-300/30">2</Badge></TableCell>
                      <TableCell className="font-medium">StormMage</TableCell>
                      <TableCell>98</TableCell>
                      <TableCell>14,890</TableCell>
                      <TableCell className="text-right">50,000 <span className="text-yellow-400">★</span></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><Badge className="bg-orange-500/20 text-orange-300 border-orange-500/30">3</Badge></TableCell>
                      <TableCell className="font-medium">ShadowHunter</TableCell>
                      <TableCell>97</TableCell>
                      <TableCell>14,550</TableCell>
                      <TableCell className="text-right">25,000 <span className="text-yellow-400">★</span></TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Top Guildes */}
            <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/10 to-purple-500/5 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Users className="h-6 w-6 text-purple-400" />
                  <div>
                    <CardTitle>Top Guildes</CardTitle>
                    <CardDescription>Les guildes les plus puissantes</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">Rang</TableHead>
                      <TableHead>Guilde</TableHead>
                      <TableHead>Membres</TableHead>
                      <TableHead>Territoire</TableHead>
                      <TableHead className="text-right">Points</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell><Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">1</Badge></TableCell>
                      <TableCell className="font-medium">Les Gardiens de l'Aube</TableCell>
                      <TableCell>50/50</TableCell>
                      <TableCell>Citadelle du Nord</TableCell>
                      <TableCell className="text-right">125,430</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><Badge className="bg-slate-300/20 text-slate-300 border-slate-300/30">2</Badge></TableCell>
                      <TableCell className="font-medium">Ordre des Tempêtes</TableCell>
                      <TableCell>48/50</TableCell>
                      <TableCell>Vallée des Vents</TableCell>
                      <TableCell className="text-right">118,920</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><Badge className="bg-orange-500/20 text-orange-300 border-orange-500/30">3</Badge></TableCell>
                      <TableCell className="font-medium">Les Marchands de l'Est</TableCell>
                      <TableCell>45/50</TableCell>
                      <TableCell>Port des Brumes</TableCell>
                      <TableCell className="text-right">98,750</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Top PvP */}
            <Card className="border-red-500/20 bg-gradient-to-br from-red-500/10 to-pink-500/5 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Swords className="h-6 w-6 text-red-400" />
                  <div>
                    <CardTitle>Top PvP</CardTitle>
                    <CardDescription>Les combattants les plus redoutables</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">Rang</TableHead>
                      <TableHead>Joueur</TableHead>
                      <TableHead>Victoires</TableHead>
                      <TableHead>K/D Ratio</TableHead>
                      <TableHead className="text-right">Points PvP</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell><Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">1</Badge></TableCell>
                      <TableCell className="font-medium">Blademaster</TableCell>
                      <TableCell>1,245</TableCell>
                      <TableCell>3.8</TableCell>
                      <TableCell className="text-right">8,920</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><Badge className="bg-slate-300/20 text-slate-300 border-slate-300/30">2</Badge></TableCell>
                      <TableCell className="font-medium">ShadowAssassin</TableCell>
                      <TableCell>1,180</TableCell>
                      <TableCell>3.5</TableCell>
                      <TableCell className="text-right">8,450</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><Badge className="bg-orange-500/20 text-orange-300 border-orange-500/30">3</Badge></TableCell>
                      <TableCell className="font-medium">ThunderKnight</TableCell>
                      <TableCell>1,020</TableCell>
                      <TableCell>3.2</TableCell>
                      <TableCell className="text-right">7,890</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
      </main>
    </div>
  )
}