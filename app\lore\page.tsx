"use client"

import { useState } from "react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ChevronLeft, ChevronRight, BookOpen, Clock, Users } from "lucide-react"
import { motion } from "framer-motion"

// Données des chapitres du lore
const loreChapters = [
  {
    id: 1,
    title: "Les Origines du Royaume",
    subtitle: "La naissance d'un monde mystique",
    readTime: "5 min",
    content: {
      introduction: "Il y a fort longtemps, dans les terres mystérieuses des Tempêtes Éternelles...",
      mainText: "Le Royaume des Tempêtes tire son nom des puissantes forces élémentaires qui ont façonné ses terres. Les anciens racontent que ce royaume est né de la fusion entre le monde des mortels et celui des esprits, créant un lieu où la magie et la réalité s'entremêlent.",
      sections: [
        {
          title: "La Grande Convergence",
          text: "Lorsque les premiers humains découvrirent ces terres, ils furent témoins d'un phénomène extraordinaire : la convergence des plans élémentaires. Les tempêtes n'étaient pas de simples phénomènes météorologiques, mais des manifestations de la magie pure."
        },
        {
          title: "Les Premiers Habitants",
          text: "Les tribus nomades qui s'installèrent dans ces terres apprirent rapidement à respecter et à comprendre les forces qui les entouraient. Ils développèrent les premières techniques de maîtrise des tempêtes."
        }
      ]
    }
  },
  {
    id: 2,
    title: "L'Ère de l'Éveil",
    subtitle: "Les premiers pas vers la civilisation",
    readTime: "7 min",
    content: {
      introduction: "Entre les années 0 et 500, le royaume connut sa première grande transformation...",
      mainText: "L'Ère de l'Éveil marque le début de la civilisation organisée dans le Royaume des Tempêtes. C'est durant cette période que furent fondées les premières cités et que la magie des tempêtes fut codifiée.",
      sections: [
        {
          title: "Fondation de la Première École",
          text: "Aldric le Premier Mage établit la première école de magie dans ce qui deviendrait plus tard la capitale. Cette institution révolutionna l'apprentissage des arts mystiques."
        },
        {
          title: "Les Premières Guildes",
          text: "Les artisans et les mages commencèrent à s'organiser en guildes, créant les bases du système social qui perdure encore aujourd'hui."
        }
      ]
    }
  },
  {
    id: 3,
    title: "L'Ère des Conflits",
    subtitle: "Guerres et alliances",
    readTime: "8 min",
    content: {
      introduction: "De 501 à 1000, le royaume fut secoué par de nombreux conflits...",
      mainText: "Cette période sombre de l'histoire fut marquée par les guerres entre guildes et l'émergence des premiers grands champions. Les alliances se formaient et se brisaient au gré des intérêts politiques.",
      sections: [
        {
          title: "La Guerre des Trois Vents",
          text: "Le conflit le plus célèbre de cette époque opposa trois grandes alliances de guildes pour le contrôle des routes commerciales principales."
        },
        {
          title: "La Couronne des Tempêtes",
          text: "Cet artefact légendaire, capable de contrôler les tempêtes elles-mêmes, fut perdu durant cette période et n'a jamais été retrouvé."
        }
      ]
    }
  },
  {
    id: 4,
    title: "L'Ère de la Prospérité",
    subtitle: "L'âge moderne du royaume",
    readTime: "6 min",
    content: {
      introduction: "Depuis l'an 1001, le royaume connaît une période de paix et de prospérité...",
      mainText: "L'âge actuel est marqué par la paix entre les guildes et l'exploration de nouvelles terres. Les aventuriers parcourent le royaume à la recherche de gloire et de richesses.",
      sections: [
        {
          title: "Les Portails de Téléportation",
          text: "L'invention de ces portails magiques a révolutionné les voyages et le commerce, connectant les régions les plus éloignées du royaume."
        },
        {
          title: "Les Mines de Cristaux d'Orage",
          text: "La découverte de ces mines a apporté une nouvelle source de richesse et de pouvoir magique au royaume."
        }
      ]
    }
  },
  {
    id: 5,
    title: "Les Grandes Factions",
    subtitle: "Les forces qui façonnent le royaume",
    readTime: "9 min",
    content: {
      introduction: "Plusieurs factions influentes dirigent les destinées du royaume...",
      mainText: "Ces organisations puissantes maintiennent l'équilibre politique et social du Royaume des Tempêtes, chacune avec ses propres objectifs et méthodes.",
      sections: [
        {
          title: "Les Gardiens de l'Orage",
          text: "Protecteurs ancestraux des secrets de la magie des tempêtes, ils maintiennent l'équilibre des forces élémentaires et forment les nouveaux mages."
        },
        {
          title: "La Ligue des Marchands",
          text: "Ce consortium puissant contrôle les routes commerciales et influence grandement l'économie du royaume grâce à son réseau étendu."
        },
        {
          title: "Les Chasseurs de l'Aube",
          text: "Groupe d'élite spécialisé dans la traque des créatures corrompues par les tempêtes, ils protègent les citoyens des dangers mystiques."
        }
      ]
    }
  }
]

export default function LorePage() {
  const [currentChapter, setCurrentChapter] = useState(1)
  const [isReading, setIsReading] = useState(false)

  const chapter = loreChapters.find(ch => ch.id === currentChapter) || loreChapters[0]
  const totalChapters = loreChapters.length

  const nextChapter = () => {
    if (currentChapter < totalChapters) {
      setCurrentChapter(currentChapter + 1)
    }
  }

  const prevChapter = () => {
    if (currentChapter > 1) {
      setCurrentChapter(currentChapter - 1)
    }
  }

  const goToChapter = (chapterId: number) => {
    setCurrentChapter(chapterId)
    setIsReading(true)
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur supports-[backdrop-filter]:bg-base-100/80 px-4 shadow-sm relative z-10">
        <SidebarTrigger className="-ml-1 hover:bg-primary/10 transition-colors" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-base-300/50" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/" className="text-base-content/60 hover:text-base-content transition-colors">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-base-content/40" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Lore</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 p-6 min-h-screen relative overflow-hidden">
        {/* Consistent Background Image */}
        <motion.div
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 0.15, scale: 1 }}
          transition={{ duration: 2 }}
          className="absolute inset-0 z-0"
        >
          <img src="/image3.png" alt="" className="w-full h-full object-cover opacity-15 mix-blend-soft-light" />
        </motion.div>

        <div className="relative z-10 max-w-7xl mx-auto">
          {!isReading ? (
            // Chapter Selection View
            <div className="space-y-8">
              {/* Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center space-y-4"
              >
                <div className="flex items-center justify-center gap-3 mb-4">
                  <BookOpen className="h-8 w-8 text-primary" />
                  <h1 className="text-4xl font-bold text-base-content">Chroniques du Royaume</h1>
                </div>
                <p className="text-lg text-base-content/70 max-w-2xl mx-auto">
                  Découvrez l'histoire fascinante du Royaume des Tempêtes à travers ses différentes époques
                </p>
              </motion.div>

              {/* Chapter Grid */}
              <motion.div
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"
              >
                {loreChapters.map((chapterItem, index) => (
                  <motion.div
                    key={chapterItem.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -5 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card
                      className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-full"
                      onClick={() => goToChapter(chapterItem.id)}
                    >
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <Badge className="bg-primary/20 text-primary border-primary/30">
                            Chapitre {chapterItem.id}
                          </Badge>
                          <div className="flex items-center gap-1 text-xs text-base-content/60">
                            <Clock className="h-3 w-3" />
                            {chapterItem.readTime}
                          </div>
                        </div>
                        <CardTitle className="text-base-content text-xl">
                          {chapterItem.title}
                        </CardTitle>
                        <CardDescription className="text-base-content/70">
                          {chapterItem.subtitle}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-base-content/80 line-clamp-3">
                          {chapterItem.content.introduction}
                        </p>
                        <div className="mt-4 flex items-center justify-between">
                          <span className="text-xs text-base-content/60">
                            {chapterItem.content.sections.length} sections
                          </span>
                          <ChevronRight className="h-4 w-4 text-primary" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          ) : (
            // Chapter Reading View
            <div className="space-y-8">
              {/* Chapter Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-4"
              >
                <Button
                  variant="outline"
                  onClick={() => setIsReading(false)}
                  className="mb-4 border-base-300 hover:bg-base-200"
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Retour aux chapitres
                </Button>

                <div className="text-center space-y-2">
                  <Badge className="bg-primary/20 text-primary border-primary/30">
                    Chapitre {chapter.id} sur {totalChapters}
                  </Badge>
                  <h1 className="text-3xl font-bold text-base-content">{chapter.title}</h1>
                  <p className="text-lg text-base-content/70">{chapter.subtitle}</p>
                  <div className="flex items-center justify-center gap-4 text-sm text-base-content/60">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {chapter.readTime}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {chapter.content.sections.length} sections
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Chapter Content */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
              >
                <Card className="bg-base-100/95 backdrop-blur border-primary/20 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-base-content text-2xl">
                      {chapter.title}
                    </CardTitle>
                    <CardDescription className="text-base-content/70 text-lg">
                      {chapter.content.introduction}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="prose prose-lg max-w-none">
                      <p className="text-base-content leading-relaxed">
                        {chapter.content.mainText}
                      </p>
                    </div>

                    {/* Chapter Sections */}
                    <div className="space-y-6">
                      {chapter.content.sections.map((section, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                          className="border-l-4 border-primary/30 pl-6 py-4 bg-primary/5 rounded-r-lg"
                        >
                          <h3 className="text-xl font-semibold text-base-content mb-3">
                            {section.title}
                          </h3>
                          <p className="text-base-content/80 leading-relaxed">
                            {section.text}
                          </p>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Navigation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="flex items-center justify-between"
              >
                <Button
                  variant="outline"
                  onClick={prevChapter}
                  disabled={currentChapter === 1}
                  className="border-base-300 hover:bg-base-200 disabled:opacity-50"
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Chapitre précédent
                </Button>

                {/* Page Indicators */}
                <div className="flex items-center gap-2">
                  {loreChapters.map((_, index) => (
                    <button
                      key={index + 1}
                      onClick={() => setCurrentChapter(index + 1)}
                      className={`w-8 h-8 rounded-full text-sm font-medium transition-all duration-200 ${
                        currentChapter === index + 1
                          ? 'bg-primary text-primary-content'
                          : 'bg-base-200 text-base-content hover:bg-base-300'
                      }`}
                    >
                      {index + 1}
                    </button>
                  ))}
                </div>

                <Button
                  variant="outline"
                  onClick={nextChapter}
                  disabled={currentChapter === totalChapters}
                  className="border-base-300 hover:bg-base-200 disabled:opacity-50"
                >
                  Chapitre suivant
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              </motion.div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}