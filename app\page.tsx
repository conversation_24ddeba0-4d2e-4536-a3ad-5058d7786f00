"use client"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { MainDashboard } from "@/components/main-dashboard"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

export default function Page() {
  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300/50 bg-base-100/95 backdrop-blur supports-[backdrop-filter]:bg-base-100/80 px-4 shadow-sm relative z-10">
        <SidebarTrigger className="-ml-1 hover:bg-primary/10 transition-colors" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-base-300/50" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="#" className="text-base-content/60 hover:text-primary transition-colors">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-base-content/30" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content font-medium">Accueil</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <div className="relative z-10">
        <MainDashboard />
      </div>
    </>
  )
}
