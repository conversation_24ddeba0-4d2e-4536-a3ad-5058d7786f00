"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { motion } from "framer-motion"
import {
  BookOpen,
  FileText,
  Home,
  MessageSquare,
  Settings,
  Shield,
  Crown,
  ChevronDown,
  ExternalLink,
  LogOut,
  UserCircle,
} from "lucide-react"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import { ThemeToggle } from "@/components/theme-toggle"

// Menu items
const items = [
  {
    title: "Accueil",
    url: "/",
    icon: Home,
  },
  {
    title: "Règlement",
    url: "/reglement",
    icon: FileText,
  },
  {
    title: "Lore",
    url: "/lore",
    icon: BookOpen,
  },
  {
    title: "Classements",
    url: "/classements",
    icon: Crown,
  },
]

const gameItems = [
  {
    title: "Serveurs",
    url: "/serveurs",
    icon: Shield,
  },
]

const socialItems = [
  {
    title: "Discord",
    url: "https://discord.gg/cbQbt6jHTS",
    icon: MessageSquare,
    external: true,
  },
  {
    title: "Support",
    url: "https://discord.gg/cbQbt6jHTS",
    icon: Settings,
    external: true,
  },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()

  return (
    <Sidebar variant="inset" className="bg-base-100/95 backdrop-blur border-base-300/50 shadow-lg" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="flex flex-col items-center p-4">
              <motion.div
                whileHover={{ scale: 1.05, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                className="w-16 h-16 mb-3 shadow-lg rounded-xl overflow-hidden"
              >
                <img
                  src="/logo_without_bg.png"
                  alt="Le Royaume Des Tempêtes"
                  className="w-full h-full object-contain"
                />
              </motion.div>
              <div className="text-center">
                <h1 className="text-lg font-bold text-base-content">
                  Le Royaume
                </h1>
                <h2 className="text-sm font-semibold text-primary">
                  Des Tempêtes
                </h2>
                <Badge className="mt-1 text-xs bg-accent/20 text-accent border-accent/30">
                  #SOON
                </Badge>
              </div>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="text-base-content/70">Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <motion.div
                  key={item.title}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      isActive={pathname === item.url}
                      className="hover:bg-primary/10 hover:text-primary transition-all duration-200"
                    >
                      <a href={item.url}>
                        <motion.div
                          whileHover={{ rotate: 360 }}
                          transition={{ duration: 0.3 }}
                        >
                          <item.icon />
                        </motion.div>
                        <span>{item.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </motion.div>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel className="text-base-content/70">Jeu</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {gameItems.map((item) => (
                <motion.div
                  key={item.title}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      isActive={pathname === item.url}
                      className="hover:bg-secondary/10 hover:text-secondary transition-all duration-200"
                    >
                      <a href={item.url}>
                        <motion.div
                          whileHover={{ rotate: 360 }}
                          transition={{ duration: 0.3 }}
                        >
                          <item.icon />
                        </motion.div>
                        <span>{item.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </motion.div>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel className="text-base-content/70">Communauté</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {socialItems.map((item) => (
                <motion.div
                  key={item.title}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      className="hover:bg-accent/10 hover:text-accent transition-all duration-200"
                    >
                      <a
                        href={item.url}
                        target={item.external ? "_blank" : undefined}
                        rel={item.external ? "noopener noreferrer" : undefined}
                      >
                        <motion.div
                          whileHover={{ rotate: 360 }}
                          transition={{ duration: 0.3 }}
                        >
                          <item.icon />
                        </motion.div>
                        <span>{item.title}</span>
                        {item.external && (
                          <motion.div
                            whileHover={{ scale: 1.2 }}
                            transition={{ duration: 0.2 }}
                          >
                            <ExternalLink className="ml-auto h-4 w-4" />
                          </motion.div>
                        )}
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </motion.div>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="flex items-center justify-between p-2">
              <ThemeToggle />
            </div>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <SidebarMenuButton
                    size="lg"
                    className="data-[state=open]:bg-primary/10 data-[state=open]:text-primary hover:bg-primary/5 transition-all duration-200"
                  >
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage src="/placeholder.svg?height=32&width=32" alt="Taxhaa" />
                      <AvatarFallback className="rounded-lg bg-primary text-primary-content">TX</AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold text-base-content">Taxhaa</span>
                      <span className="truncate text-xs text-base-content/60">Joueur</span>
                    </div>
                    <motion.div
                      whileHover={{ rotate: 180 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="ml-auto size-4" />
                    </motion.div>
                  </SidebarMenuButton>
                </motion.div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg bg-base-100 border-base-300"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem className="hover:bg-primary/10 hover:text-primary">
                  <a href="/profile" className="flex items-center w-full">
                    <UserCircle className="mr-2 h-4 w-4" />
                    Voir le profil
                  </a>
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:bg-primary/10 hover:text-primary">
                  <a href="/settings" className="flex items-center w-full">
                    <Settings className="mr-2 h-4 w-4" />
                    Paramètres
                  </a>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-base-300" />
                <DropdownMenuItem className="hover:bg-error/10 hover:text-error">
                  <a href="/login" className="flex items-center w-full">
                    <LogOut className="mr-2 h-4 w-4" />
                    Se déconnecter
                  </a>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
