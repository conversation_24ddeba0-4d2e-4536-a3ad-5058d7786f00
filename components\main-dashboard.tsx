"use client"

import { useState } from "react"
import {
  Users,
  ShoppingCart,
  MessageSquare,
  Crown,
  Target,
  ExternalLink,
  TrendingUp,
  Activity,
  Zap,
  Server,
  Clock,
  Wifi,
  Sword,
  Shield,
  Trophy,
} from "lucide-react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const cardVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
}

const statsCardVariants = {
  hidden: { scale: 0.8, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  },
}

export function MainDashboard() {
  const [onlinePlayers] = useState(123)
  const maxPlayers = 200

  return (
    <div className="flex-1 space-y-6 p-6 min-h-screen relative overflow-hidden">
      {/* Decorative Images */}
      <motion.div
        initial={{ opacity: 0, x: -100, rotate: -10 }}
        animate={{ opacity: 0.15, x: 0, rotate: 0 }}
        transition={{ duration: 1.2, delay: 0.3 }}
        className="absolute top-10 left-8 w-24 h-24 z-0"
      >
        <img src="/image1.png" alt="" className="w-full h-full object-contain opacity-40" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: -100, rotate: 10 }}
        animate={{ opacity: 0.2, y: 0, rotate: 0 }}
        transition={{ duration: 1.2, delay: 0.6 }}
        className="absolute top-32 right-12 w-20 h-20 z-0"
      >
        <img src="/image2.png" alt="" className="w-full h-full object-contain opacity-30" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, x: 100, rotate: -15 }}
        animate={{ opacity: 0.18, x: 0, rotate: 0 }}
        transition={{ duration: 1.2, delay: 0.9 }}
        className="absolute bottom-20 right-16 w-28 h-28 z-0"
      >
        <img src="/image3.png" alt="" className="w-full h-full object-contain opacity-50" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 100, rotate: 20 }}
        animate={{ opacity: 0.12, y: 0, rotate: 0 }}
        transition={{ duration: 1.2, delay: 1.2 }}
        className="absolute bottom-32 left-10 w-32 h-32 z-0"
      >
        <img src="/image1.png" alt="" className="w-full h-full object-contain opacity-25" />
      </motion.div>

      {/* Welcome Section */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative z-10"
      >
        <motion.div variants={cardVariants} className="text-center mb-8">
          <motion.div
            whileHover={{ scale: 1.05, rotate: 5 }}
            className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg"
          >
            <Crown className="w-8 h-8 text-primary-content" />
          </motion.div>
          <h1 className="text-3xl font-bold text-base-content mb-2">Bienvenue dans le Royaume</h1>
          <p className="text-base-content/70 text-lg">Découvrez les dernières nouvelles et statistiques</p>
        </motion.div>

        {/* Top Stats Row */}
        <motion.div
          variants={containerVariants}
          className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8"
        >
          <motion.div variants={statsCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="bg-base-100/95 backdrop-blur border-success/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-base-content">Joueurs en ligne</CardTitle>
                <motion.div
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <Users className="h-5 w-5 text-success" />
                </motion.div>
              </CardHeader>
              <CardContent>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                  className="text-2xl font-bold text-base-content"
                >
                  {onlinePlayers}/{maxPlayers}
                </motion.div>
                <Progress 
                  value={(onlinePlayers / maxPlayers) * 100} 
                  className="mt-2 h-2" 
                />
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-xs text-success mt-1"
                >
                  +12% par rapport à hier
                </motion.p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={statsCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="bg-base-100/95 backdrop-blur border-warning/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-base-content">Le plus riche</CardTitle>
                <motion.div
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <Crown className="h-5 w-5 text-warning" />
                </motion.div>
              </CardHeader>
              <CardContent>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                  className="text-2xl font-bold text-base-content"
                >
                  Draogor
                </motion.div>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-xs text-warning"
                >
                  110,155,810 pièces d'or
                </motion.p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={statsCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="bg-base-100/95 backdrop-blur border-error/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-base-content">Most Wanted</CardTitle>
                <motion.div
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <Target className="h-5 w-5 text-error" />
                </motion.div>
              </CardHeader>
              <CardContent>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                  className="text-2xl font-bold text-base-content"
                >
                  Jashin
                </motion.div>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-xs text-error"
                >
                  Prime: 42,500,000
                </motion.p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={statsCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="bg-base-100/95 backdrop-blur border-info/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-base-content">Activité</CardTitle>
                <motion.div
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <Activity className="h-5 w-5 text-info" />
                </motion.div>
              </CardHeader>
              <CardContent>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                  className="text-2xl font-bold text-base-content"
                >
                  Élevée
                </motion.div>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-xs text-info"
                >
                  +23% d'activité
                </motion.p>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Main Content Grid */}
        <motion.div
          variants={containerVariants}
          className="grid gap-6 lg:grid-cols-3"
        >
          {/* Hero Section - Takes 2 columns */}
          <motion.div variants={cardVariants} className="lg:col-span-2">
            <Card className="relative overflow-hidden bg-base-100/95 backdrop-blur border-primary/30 shadow-xl h-80 hover:shadow-2xl transition-all duration-300">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20"></div>
              <CardContent className="relative h-full flex flex-col justify-center p-8">
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.3, type: "spring" }}
                >
                  <Badge className="w-fit mb-4 bg-error/20 text-error border-error/30">
                    WANTED
                  </Badge>
                </motion.div>
                <motion.h1
                  initial={{ x: -50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.4, type: "spring" }}
                  className="text-4xl font-bold text-base-content mb-4"
                >
                  Nouveaux Aventuriers Recherchés
                </motion.h1>
                <motion.p
                  initial={{ x: -50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.5, type: "spring" }}
                  className="text-xl text-base-content/80 mb-6 max-w-md"
                >
                  De nouveaux aventuriers bien équipés... Peut-être que la boutique a ce qu'il te faut ?
                </motion.p>
                <motion.div
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.6, type: "spring" }}
                  className="flex gap-4"
                >
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-content shadow-lg" asChild>
                      <a href="#" target="_blank" rel="noopener noreferrer">
                        <ShoppingCart className="w-5 h-5 mr-2" />
                        Boutique Tebex
                        <ExternalLink className="w-4 h-4 ml-2" />
                      </a>
                    </Button>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button size="lg" variant="outline" className="border-primary/30 hover:bg-primary/10 shadow-lg" asChild>
                      <a href="https://discord.gg/cbQbt6jHTS" target="_blank" rel="noopener noreferrer">
                        <MessageSquare className="w-5 h-5 mr-2" />
                        Discord
                      </a>
                    </Button>
                  </motion.div>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Right Column */}
          <motion.div variants={cardVariants} className="space-y-6">
            {/* Quick Actions */}
            <Card className="bg-base-100/95 backdrop-blur border-base-300 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader>
                <CardTitle className="text-base-content flex items-center">
                  <motion.div
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Zap className="w-5 h-5 mr-2 text-accent" />
                  </motion.div>
                  Actions Rapides
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button className="w-full bg-[#5865F2] hover:bg-[#4752C4] text-white shadow-lg" asChild>
                    <a href="https://discord.gg/cbQbt6jHTS" target="_blank" rel="noopener noreferrer">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Rejoindre Discord
                      <ExternalLink className="w-4 h-4 ml-auto" />
                    </a>
                  </Button>
                </motion.div>

                <div className="grid grid-cols-3 gap-2">
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button variant="outline" size="sm" className="border-base-300 hover:bg-base-200" asChild>
                      <a href="/pvp" title="PvP">
                        <Sword className="w-4 h-4" />
                      </a>
                    </Button>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button variant="outline" size="sm" className="border-base-300 hover:bg-base-200" asChild>
                      <a href="/guildes" title="Guildes">
                        <Shield className="w-4 h-4" />
                      </a>
                    </Button>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button variant="outline" size="sm" className="border-base-300 hover:bg-base-200" asChild>
                      <a href="/classements" title="Classements">
                        <Trophy className="w-4 h-4" />
                      </a>
                    </Button>
                  </motion.div>
                </div>
              </CardContent>
            </Card>

            {/* Server Status */}
            <Card className="bg-base-100/95 backdrop-blur border-base-300 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader>
                <CardTitle className="text-base-content flex items-center">
                  <motion.div
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Server className="w-5 h-5 mr-2 text-info" />
                  </motion.div>
                  Statut du Serveur
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="flex items-center justify-between"
                  >
                    <span className="text-sm text-base-content/70">Serveur Principal</span>
                    <Badge className="bg-success/20 text-success border-success/30">En ligne</Badge>
                  </motion.div>
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    className="flex items-center justify-between"
                  >
                    <span className="text-sm text-base-content/70 flex items-center">
                      <Wifi className="w-3 h-3 mr-1" />
                      Latence
                    </span>
                    <span className="text-sm text-base-content">23ms</span>
                  </motion.div>
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    className="flex items-center justify-between"
                  >
                    <span className="text-sm text-base-content/70 flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      Uptime
                    </span>
                    <span className="text-sm text-base-content">99.9%</span>
                  </motion.div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Support Section */}
        <motion.div
          variants={cardVariants}
          transition={{ delay: 0.8 }}
          className="mt-8"
        >
          <Card className="bg-base-100/95 backdrop-blur border-base-300 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-base-content">Support & Assistance</CardTitle>
                  <CardDescription className="text-base-content/60">
                    Besoin d'aide ? Notre équipe est là pour vous
                  </CardDescription>
                </div>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button variant="ghost" size="sm" className="text-primary hover:text-primary hover:bg-primary/10">
                    Voir tous les tickets
                  </Button>
                </motion.div>
              </div>
            </CardHeader>
            <CardContent>
              <motion.div
                variants={containerVariants}
                className="grid md:grid-cols-3 gap-4"
              >
                <motion.div variants={cardVariants} whileHover={{ scale: 1.02 }}>
                  <Card className="bg-primary/5 border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm text-primary flex items-center">
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Problème Technique
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-xs text-base-content/60 mb-4">
                        Contactez notre support pour obtenir de l'aide technique.
                      </p>
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button size="sm" className="w-full bg-primary hover:bg-primary/90 text-primary-content" asChild>
                          <a href="https://discord.gg/cbQbt6jHTS" target="_blank" rel="noopener noreferrer">
                            Ouvrir un ticket
                            <ExternalLink className="w-3 h-3 ml-2" />
                          </a>
                        </Button>
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div variants={cardVariants} whileHover={{ scale: 1.02 }}>
                  <Card className="bg-secondary/5 border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm text-secondary flex items-center">
                        <TrendingUp className="w-4 h-4 mr-2" />
                        Partenariat Streamer
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-xs text-base-content/60 mb-4">
                        Vous êtes streamer ? Discutons d'un partenariat.
                      </p>
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full border-secondary/30 hover:bg-secondary/10"
                          asChild
                        >
                          <a href="https://discord.gg/cbQbt6jHTS" target="_blank" rel="noopener noreferrer">
                            Ouvrir un ticket
                            <ExternalLink className="w-3 h-3 ml-2" />
                          </a>
                        </Button>
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div variants={cardVariants} whileHover={{ scale: 1.02 }}>
                  <Card className="bg-error/5 border-error/20 hover:border-error/40 transition-all duration-300 hover:shadow-lg">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm text-error flex items-center">
                        <Target className="w-4 h-4 mr-2" />
                        Demande de déban
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-xs text-base-content/60 mb-4">
                        Vous avez été banni ? Soumettez une demande.
                      </p>
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full border-error/30 hover:bg-error/10"
                          asChild
                        >
                          <a href="https://discord.gg/cbQbt6jHTS" target="_blank" rel="noopener noreferrer">
                            Ouvrir un ticket
                            <ExternalLink className="w-3 h-3 ml-2" />
                          </a>
                        </Button>
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </div>
  )
}
