"use client"

import * as React from "react"
import { motion } from "framer-motion"
import {
  User,
  Calendar,
  Crown,
  Shield,
  Sword,
  Trophy,
  Star,
  MapPin,
  Clock,
  MessageSquare,
} from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.4,
    },
  },
}

export default function ProfilePage() {
  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300 bg-base-100/95 backdrop-blur supports-[backdrop-filter]:bg-base-100/60 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-base-300" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/" className="text-base-content/60 hover:text-base-content">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content">Profil</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>

      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6 relative overflow-hidden">
          {/* Decorative Images */}
          <motion.div
            initial={{ opacity: 0, x: -100, rotate: -6 }}
            animate={{ opacity: 0.04, x: 0, rotate: 0 }}
            transition={{ duration: 1.2, delay: 0.3 }}
            className="absolute top-12 left-2 w-16 h-16 z-0"
          >
            <img src="/image1.png" alt="" className="w-full h-full object-contain opacity-10" />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: -100, rotate: 10 }}
            animate={{ opacity: 0.06, y: 0, rotate: 0 }}
            transition={{ duration: 1.2, delay: 0.6 }}
            className="absolute top-40 right-4 w-20 h-20 z-0"
          >
            <img src="/image2.png" alt="" className="w-full h-full object-contain opacity-15" />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 100, rotate: -14 }}
            animate={{ opacity: 0.08, x: 0, rotate: 0 }}
            transition={{ duration: 1.2, delay: 0.9 }}
            className="absolute bottom-12 right-6 w-24 h-24 z-0"
          >
            <img src="/image3.png" alt="" className="w-full h-full object-contain opacity-20" />
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6 relative z-10"
          >
            <motion.div variants={itemVariants} className="flex items-center space-x-2">
              <User className="h-6 w-6 text-primary" />
              <h1 className="text-3xl font-bold text-base-content">Profil du Joueur</h1>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Profile Card */}
              <motion.div variants={itemVariants} className="lg:col-span-1">
                <Card className="bg-base-100 border-base-300">
                  <CardHeader className="text-center">
                    <div className="flex justify-center mb-4">
                      <Avatar className="h-24 w-24">
                        <AvatarImage src="/placeholder.svg?height=96&width=96" alt="Taxhaa" />
                        <AvatarFallback className="bg-primary text-primary-content text-2xl">TX</AvatarFallback>
                      </Avatar>
                    </div>
                    <CardTitle className="text-xl">Taxhaa le Conquérant</CardTitle>
                    <CardDescription>@Taxhaa</CardDescription>
                    <div className="flex justify-center mt-2">
                      <Badge className="bg-warning/20 text-warning border-warning/30">
                        <Crown className="h-4 w-4 mr-1" />
                        Seigneur de Guerre
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-2 text-sm text-base-content/70">
                      <MessageSquare className="h-4 w-4 text-[#5865F2]" />
                      <span>Taxhaa#1234</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-base-content/70">
                      <Calendar className="h-4 w-4" />
                      <span>Membre depuis Mars 2024</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-base-content/70">
                      <MapPin className="h-4 w-4" />
                      <span>Royaume des Tempêtes</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-base-content/70">
                      <Clock className="h-4 w-4" />
                      <span>Dernière connexion: Il y a 2h</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Stats Cards */}
              <motion.div variants={itemVariants} className="lg:col-span-2 space-y-6">
                {/* Bio */}
                <Card className="bg-base-100 border-base-300">
                  <CardHeader>
                    <CardTitle>À propos</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-base-content/80">
                      Guerrier légendaire du Royaume des Tempêtes, maître des arts martiaux et protecteur des terres mystiques. 
                      Spécialisé dans les combats épiques et la stratégie militaire, j'ai consacré ma vie à défendre notre royaume 
                      contre les forces obscures qui menacent notre paix.
                    </p>
                  </CardContent>
                </Card>

                {/* Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="bg-base-100 border-base-300">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Niveau</CardTitle>
                      <Star className="h-4 w-4 text-warning" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-primary">87</div>
                      <div className="space-y-2 mt-2">
                        <div className="flex justify-between text-xs text-base-content/60">
                          <span>XP: 245,680 / 250,000</span>
                          <span>98%</span>
                        </div>
                        <Progress value={98} className="h-2" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-base-100 border-base-300">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Victoires PvP</CardTitle>
                      <Sword className="h-4 w-4 text-error" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-error">1,247</div>
                      <p className="text-xs text-base-content/60 mt-1">
                        Ratio: 78% de victoires
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-base-100 border-base-300">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Défenses</CardTitle>
                      <Shield className="h-4 w-4 text-info" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-info">892</div>
                      <p className="text-xs text-base-content/60 mt-1">
                        Forteresses protégées
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-base-100 border-base-300">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Trophées</CardTitle>
                      <Trophy className="h-4 w-4 text-warning" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-warning">23</div>
                      <p className="text-xs text-base-content/60 mt-1">
                        Tournois remportés
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Recent Achievements */}
                <Card className="bg-base-100 border-base-300">
                  <CardHeader>
                    <CardTitle>Succès récents</CardTitle>
                    <CardDescription>Vos derniers exploits dans le royaume</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-warning/20 rounded-full flex items-center justify-center">
                        <Crown className="h-4 w-4 text-warning" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Maître Conquérant</p>
                        <p className="text-sm text-base-content/60">Remporté 100 batailles consécutives</p>
                      </div>
                      <Badge variant="outline">Légendaire</Badge>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-info/20 rounded-full flex items-center justify-center">
                        <Shield className="h-4 w-4 text-info" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Gardien Éternel</p>
                        <p className="text-sm text-base-content/60">Défendu le royaume pendant 365 jours</p>
                      </div>
                      <Badge variant="outline">Épique</Badge>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-error/20 rounded-full flex items-center justify-center">
                        <Sword className="h-4 w-4 text-error" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Lame Sanglante</p>
                        <p className="text-sm text-base-content/60">Éliminé 1000 ennemis au combat</p>
                      </div>
                      <Badge variant="outline">Rare</Badge>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
        </motion.div>
      </div>
    </>
  )
}