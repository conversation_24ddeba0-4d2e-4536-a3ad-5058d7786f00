"use client"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Users, Wifi, Clock, Shield, Sword, Map } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { motion } from "framer-motion"

export default function ServeursPage() {
  return (
    <div className="dark">
      <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Serveurs</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 space-y-6 p-6 min-h-screen relative overflow-hidden">
            {/* Full Screen Background Images */}
            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.05, scale: 1 }}
              transition={{ duration: 3, delay: 0.3 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image1.png" alt="" className="w-full h-full object-cover opacity-15" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.04, scale: 1 }}
              transition={{ duration: 3, delay: 0.8 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image2.png" alt="" className="w-full h-full object-cover opacity-12 mix-blend-overlay" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.06, scale: 1 }}
              transition={{ duration: 3, delay: 1.3 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image3.png" alt="" className="w-full h-full object-cover opacity-18 mix-blend-soft-light" />
            </motion.div>

            {/* Serveur Principal */}
            <Card className="bg-base-100/95 backdrop-blur border-blue-500/20 shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Shield className="h-6 w-6 text-blue-400" />
                    <div>
                      <div className="flex items-center gap-2">
                        <CardTitle>Serveur Principal</CardTitle>
                        <Badge className="bg-green-500/20 text-green-300">En ligne</Badge>
                      </div>
                      <CardDescription>Le royaume principal des Tempêtes</CardDescription>
                    </div>
                  </div>
                  <Button variant="outline" className="border-blue-500/20 hover:bg-blue-500/10">
                    Rejoindre
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="flex items-center gap-2 p-2 rounded bg-blue-500/10">
                    <Users className="h-4 w-4 text-blue-400" />
                    <div className="text-sm">
                      <p className="text-muted-foreground">Joueurs</p>
                      <p className="font-medium">245/500</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded bg-blue-500/10">
                    <Wifi className="h-4 w-4 text-blue-400" />
                    <div className="text-sm">
                      <p className="text-muted-foreground">Latence</p>
                      <p className="font-medium">15ms</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded bg-blue-500/10">
                    <Clock className="h-4 w-4 text-blue-400" />
                    <div className="text-sm">
                      <p className="text-muted-foreground">Uptime</p>
                      <p className="font-medium">99.9%</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded bg-blue-500/10">
                    <Map className="h-4 w-4 text-blue-400" />
                    <div className="text-sm">
                      <p className="text-muted-foreground">Région</p>
                      <p className="font-medium">Europe</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Serveur PvP */}
            <Card className="bg-base-100/95 backdrop-blur border-red-500/20 shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Sword className="h-6 w-6 text-red-400" />
                    <div>
                      <div className="flex items-center gap-2">
                        <CardTitle>Arène PvP</CardTitle>
                        <Badge className="bg-green-500/20 text-green-300">En ligne</Badge>
                      </div>
                      <CardDescription>Serveur dédié aux combats JcJ</CardDescription>
                    </div>
                  </div>
                  <Button variant="outline" className="border-red-500/20 hover:bg-red-500/10">
                    Rejoindre
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="flex items-center gap-2 p-2 rounded bg-red-500/10">
                    <Users className="h-4 w-4 text-red-400" />
                    <div className="text-sm">
                      <p className="text-muted-foreground">Joueurs</p>
                      <p className="font-medium">125/200</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded bg-red-500/10">
                    <Wifi className="h-4 w-4 text-red-400" />
                    <div className="text-sm">
                      <p className="text-muted-foreground">Latence</p>
                      <p className="font-medium">12ms</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded bg-red-500/10">
                    <Clock className="h-4 w-4 text-red-400" />
                    <div className="text-sm">
                      <p className="text-muted-foreground">Uptime</p>
                      <p className="font-medium">99.7%</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 rounded bg-red-500/10">
                    <Map className="h-4 w-4 text-red-400" />
                    <div className="text-sm">
                      <p className="text-muted-foreground">Région</p>
                      <p className="font-medium">Europe</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Serveur Test */}
            <Card className="bg-base-100/95 backdrop-blur border-purple-500/20 shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Shield className="h-6 w-6 text-purple-400" />
                    <div>
                      <div className="flex items-center gap-2">
                        <CardTitle>Royaume Beta</CardTitle>
                        <Badge className="bg-yellow-500/20 text-yellow-300">Maintenance</Badge>
                      </div>
                      <CardDescription>Serveur de test des nouvelles fonctionnalités</CardDescription>
                    </div>
                  </div>
                  <Button variant="outline" className="border-purple-500/20 hover:bg-purple-500/10" disabled>
                    Bientôt
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Progression de la maintenance</span>
                      <span className="text-purple-400">75%</span>
                    </div>
                    <Progress value={75} className="h-2 bg-purple-500/10" />
                  </div>
                  <p className="text-sm text-muted-foreground">Mise à jour majeure en cours : Ajout de nouvelles zones et quêtes</p>
                </div>
              </CardContent>
            </Card>
      </main>
    </div>
  )
}