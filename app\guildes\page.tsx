"use client"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Users, Crown, Shield, Sword, Castle } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { motion } from "framer-motion"

export default function GuildesPage() {
  return (
    <div className="dark">
      <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Guildes</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 space-y-6 p-6 min-h-screen relative overflow-hidden">
            {/* Full Screen Background Images */}
            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.04, scale: 1 }}
              transition={{ duration: 3, delay: 0.3 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image1.png" alt="" className="w-full h-full object-cover opacity-12" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.03, scale: 1 }}
              transition={{ duration: 3, delay: 0.8 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image2.png" alt="" className="w-full h-full object-cover opacity-10 mix-blend-overlay" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.05, scale: 1 }}
              transition={{ duration: 3, delay: 1.3 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image3.png" alt="" className="w-full h-full object-cover opacity-15 mix-blend-soft-light" />
            </motion.div>

            {/* Création de Guilde */}
            <Card className="bg-base-100/95 backdrop-blur border-purple-500/20 shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Castle className="h-6 w-6 text-purple-400" />
                    <div>
                      <CardTitle className="text-base-content">Créer une Guilde</CardTitle>
                      <CardDescription className="text-base-content/70">Fondez votre propre guilde et recrutez des membres</CardDescription>
                    </div>
                  </div>
                  <Button variant="outline" className="border-purple-500/20 hover:bg-purple-500/10">
                    Créer une Guilde
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Types de Guildes */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                      <Users className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                      <h3 className="font-semibold text-blue-300">Guilde Débutant</h3>
                      <p className="text-sm text-blue-200">5 membres max</p>
                      <Badge className="mt-2 bg-blue-500/20 text-blue-300 border-blue-500/30">Niveau 1</Badge>
                    </div>
                    <div className="text-center p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                      <Shield className="h-8 w-8 text-green-400 mx-auto mb-2" />
                      <h3 className="font-semibold text-green-300">Guilde Établie</h3>
                      <p className="text-sm text-green-200">8 membres max</p>
                      <Badge className="mt-2 bg-green-500/20 text-green-300 border-green-500/30">Niveau 2</Badge>
                    </div>
                    <div className="text-center p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                      <Crown className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                      <h3 className="font-semibold text-yellow-300">Guilde Reconnue</h3>
                      <p className="text-sm text-yellow-200">10 membres max</p>
                      <Badge className="mt-2 bg-yellow-500/20 text-yellow-300 border-yellow-500/30">Niveau 3</Badge>
                    </div>
                  </div>

                  {/* Coût et Avantages */}
                  <div className="bg-accent/5 rounded-lg p-4 border border-accent/20">
                    <div className="text-center mb-4">
                      <h4 className="font-semibold text-accent mb-2">Coût de Création</h4>
                      <p className="text-2xl font-bold text-accent">35,000 Shinbans</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <h5 className="font-semibold text-base-content mb-2">✅ Inclus :</h5>
                        <ul className="space-y-1 text-base-content/70">
                          <li>• Base de guilde sécurisée</li>
                          <li>• Système de grades</li>
                          <li>• Chat de guilde privé</li>
                          <li>• Événements de guilde</li>
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-base-content mb-2">❌ Non inclus :</h5>
                        <ul className="space-y-1 text-base-content/70">
                          <li>• Coffres partagés</li>
                          <li>• (À acheter séparément)</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Niveaux de Guilde */}
            <Card className="bg-base-100/95 backdrop-blur border-blue-500/20 shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Crown className="h-6 w-6 text-blue-400" />
                  <div>
                    <CardTitle className="text-base-content">Progression de Guilde</CardTitle>
                    <CardDescription className="text-base-content/70">Développez votre guilde pour débloquer plus d'avantages</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Niveau 1 - Guilde Débutant</span>
                      <span className="text-blue-400">5 membres max</span>
                    </div>
                    <Progress value={100} className="h-2 bg-blue-500/10" />
                    <p className="text-xs text-base-content/60">Guilde de base pour commencer votre aventure</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Niveau 2 - Guilde Établie</span>
                      <span className="text-green-400">8 membres max</span>
                    </div>
                    <Progress value={60} className="h-2 bg-green-500/10" />
                    <p className="text-xs text-base-content/60">Guilde expérimentée avec plus de possibilités</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Niveau 3 - Guilde Reconnue</span>
                      <span className="text-yellow-400">10 membres max</span>
                    </div>
                    <Progress value={30} className="h-2 bg-yellow-500/10" />
                    <p className="text-xs text-base-content/60">Guilde prestigieuse reconnue dans tout le royaume</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Avantages de Guilde */}
            <Card className="bg-base-100/95 backdrop-blur border-green-500/20 shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Shield className="h-6 w-6 text-green-400" />
                  <div>
                    <CardTitle className="text-base-content">Avantages de Niveau</CardTitle>
                    <CardDescription className="text-base-content/70">Débloquez des bonus exclusifs en montant de niveau</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="p-4 rounded bg-green-500/10 border border-green-500/20">
                    <Badge className="mb-2 bg-blue-500/20 text-blue-300">Niveau 1 - Débutant</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">✅ Chat de guilde</li>
                      <li className="text-foreground">✅ Base sécurisée</li>
                      <li className="text-foreground">✅ Système de grades</li>
                      <li className="text-foreground/50">❌ Coffres partagés</li>
                    </ul>
                  </div>
                  <div className="p-4 rounded bg-green-500/10 border border-green-500/20">
                    <Badge className="mb-2 bg-green-500/20 text-green-300">Niveau 2 - Établie</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">✅ Emblème personnalisé</li>
                      <li className="text-foreground">✅ Événements de guilde</li>
                      <li className="text-foreground">✅ Bonus XP +5%</li>
                      <li className="text-foreground/50">❌ Coffres partagés</li>
                    </ul>
                  </div>
                  <div className="p-4 rounded bg-green-500/10 border border-green-500/20">
                    <Badge className="mb-2 bg-yellow-500/20 text-yellow-300">Niveau 3 - Reconnue</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">✅ Hall de guilde amélioré</li>
                      <li className="text-foreground">✅ Bonus XP +10%</li>
                      <li className="text-foreground">✅ Privilèges spéciaux</li>
                      <li className="text-foreground/50">❌ Coffres partagés</li>
                    </ul>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-warning/10 border border-warning/20 rounded-lg">
                  <p className="text-sm text-warning font-medium">
                    💡 Note : Les coffres partagés doivent être achetés séparément pour toutes les guildes.
                  </p>
                </div>
              </CardContent>
            </Card>
      </main>
    </div>
  )
}