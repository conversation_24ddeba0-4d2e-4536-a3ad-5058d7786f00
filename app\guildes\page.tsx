"use client"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Users, Crown, Shield, Sword, Coins, Castle, ScrollText, Trophy } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { motion } from "framer-motion"

export default function GuildesPage() {
  return (
    <div className="dark">
      <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Guildes</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 space-y-6 p-6 min-h-screen bg-gradient-to-br from-slate-950 via-blue-950 to-slate-950 relative overflow-hidden">
            {/* Decorative Images */}
            <motion.div
              initial={{ opacity: 0, x: -100, rotate: -8 }}
              animate={{ opacity: 0.06, x: 0, rotate: 0 }}
              transition={{ duration: 1.2, delay: 0.3 }}
              className="absolute top-16 left-4 w-20 h-20 z-0"
            >
              <img src="/image1.png" alt="" className="w-full h-full object-contain opacity-15" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: -100, rotate: 12 }}
              animate={{ opacity: 0.08, y: 0, rotate: 0 }}
              transition={{ duration: 1.2, delay: 0.6 }}
              className="absolute top-44 right-6 w-24 h-24 z-0"
            >
              <img src="/image2.png" alt="" className="w-full h-full object-contain opacity-20" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 100, rotate: -18 }}
              animate={{ opacity: 0.1, x: 0, rotate: 0 }}
              transition={{ duration: 1.2, delay: 0.9 }}
              className="absolute bottom-24 right-8 w-28 h-28 z-0"
            >
              <img src="/image3.png" alt="" className="w-full h-full object-contain opacity-25" />
            </motion.div>

            {/* Création de Guilde */}
            <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/10 to-purple-500/5 relative z-10">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Castle className="h-6 w-6 text-purple-400" />
                    <div>
                      <CardTitle>Créer une Guilde</CardTitle>
                      <CardDescription>Fondez votre propre guilde et recrutez des membres</CardDescription>
                    </div>
                  </div>
                  <Button variant="outline" className="border-purple-500/20 hover:bg-purple-500/10">
                    Créer une Guilde
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="p-4 rounded bg-purple-500/10 border border-purple-500/20">
                    <h3 className="flex items-center gap-2 font-semibold text-purple-400 mb-2">
                      <Coins className="h-4 w-4" />
                      Coût de Création
                    </h3>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">50,000 pièces d'or</li>
                      <li className="text-foreground">10 cristaux d'orage</li>
                      <li className="text-foreground">Niveau 30 minimum</li>
                    </ul>
                  </div>
                  <div className="p-4 rounded bg-purple-500/10 border border-purple-500/20">
                    <h3 className="flex items-center gap-2 font-semibold text-purple-400 mb-2">
                      <ScrollText className="h-4 w-4" />
                      Prérequis
                    </h3>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">5 membres fondateurs</li>
                      <li className="text-foreground">Nom unique</li>
                      <li className="text-foreground">Emblème personnalisé</li>
                    </ul>
                  </div>
                  <div className="p-4 rounded bg-purple-500/10 border border-purple-500/20">
                    <h3 className="flex items-center gap-2 font-semibold text-purple-400 mb-2">
                      <Trophy className="h-4 w-4" />
                      Avantages
                    </h3>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">Hall de guilde privé</li>
                      <li className="text-foreground">Coffre partagé</li>
                      <li className="text-foreground">Chat de guilde</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Niveaux de Guilde */}
            <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/10 to-blue-500/5 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Crown className="h-6 w-6 text-blue-400" />
                  <div>
                    <CardTitle>Progression de Guilde</CardTitle>
                    <CardDescription>Développez votre guilde pour débloquer plus d'avantages</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Niveau 1 - Guilde Débutante</span>
                      <span className="text-blue-400">50 membres max</span>
                    </div>
                    <Progress value={100} className="h-2 bg-blue-500/10" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Niveau 2 - Guilde Établie</span>
                      <span className="text-blue-400">100 membres max</span>
                    </div>
                    <Progress value={60} className="h-2 bg-blue-500/10" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Niveau 3 - Guilde Respectée</span>
                      <span className="text-blue-400">200 membres max</span>
                    </div>
                    <Progress value={30} className="h-2 bg-blue-500/10" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Avantages de Guilde */}
            <Card className="border-green-500/20 bg-gradient-to-br from-green-500/10 to-emerald-500/5 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Shield className="h-6 w-6 text-green-400" />
                  <div>
                    <CardTitle>Avantages de Niveau</CardTitle>
                    <CardDescription>Débloquez des bonus exclusifs en montant de niveau</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="p-4 rounded bg-green-500/10 border border-green-500/20">
                    <Badge className="mb-2 bg-blue-500/20 text-blue-300">Niveau 1</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">Chat de guilde</li>
                      <li className="text-foreground">Coffre 50 emplacements</li>
                      <li className="text-foreground">Bonus XP +5%</li>
                    </ul>
                  </div>
                  <div className="p-4 rounded bg-green-500/10 border border-green-500/20">
                    <Badge className="mb-2 bg-purple-500/20 text-purple-300">Niveau 2</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">Emblème personnalisé</li>
                      <li className="text-foreground">Coffre 100 emplacements</li>
                      <li className="text-foreground">Bonus XP +10%</li>
                    </ul>
                  </div>
                  <div className="p-4 rounded bg-green-500/10 border border-green-500/20">
                    <Badge className="mb-2 bg-yellow-500/20 text-yellow-300">Niveau 3</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">Hall de guilde amélioré</li>
                      <li className="text-foreground">Coffre illimité</li>
                      <li className="text-foreground">Bonus XP +15%</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
      </main>
    </div>
  )
}