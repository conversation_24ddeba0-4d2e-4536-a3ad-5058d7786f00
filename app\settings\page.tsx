"use client"

import * as React from "react"
import { motion } from "framer-motion"
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Camera,
  Save,
  Crown,
  Settings as SettingsIcon,
  MessageSquare,
} from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.4,
    },
  },
}

export default function SettingsPage() {
  const [isLoading, setIsLoading] = React.useState(false)
  const [saveSuccess, setSaveSuccess] = React.useState(false)

  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    // Simulate save process
    await new Promise(resolve => setTimeout(resolve, 1500))
    setIsLoading(false)
    setSaveSuccess(true)
    setTimeout(() => setSaveSuccess(false), 3000)
  }


  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b border-base-300 bg-base-100/95 backdrop-blur supports-[backdrop-filter]:bg-base-100/60 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-base-300" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/" className="text-base-content/60 hover:text-base-content">
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base-content">Paramètres</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>

      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            <motion.div variants={itemVariants} className="flex items-center space-x-2">
              <SettingsIcon className="h-6 w-6 text-primary" />
              <h1 className="text-3xl font-bold text-base-content">Paramètres</h1>
            </motion.div>

            {saveSuccess && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <Alert className="border-success bg-success/10">
                  <AlertDescription className="text-success">
                    Paramètres sauvegardés avec succès !
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}

            <motion.div variants={itemVariants}>
              <Tabs defaultValue="profile" className="space-y-4">
                <TabsList className="grid w-full grid-cols-3 lg:w-[400px] bg-base-200">
                  <TabsTrigger value="profile" className="data-[state=active]:bg-primary data-[state=active]:text-primary-content">
                    Profil
                  </TabsTrigger>
                  <TabsTrigger value="notifications" className="data-[state=active]:bg-primary data-[state=active]:text-primary-content">
                    Notifications
                  </TabsTrigger>
                  <TabsTrigger value="preferences" className="data-[state=active]:bg-primary data-[state=active]:text-primary-content">
                    Préférences
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="profile" className="space-y-4">
                  <Card className="bg-base-100 border-base-300">
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <User className="h-5 w-5 text-primary" />
                        <span>Informations du profil</span>
                      </CardTitle>
                      <CardDescription>
                        Gérez vos informations personnelles et votre profil public.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <form onSubmit={handleSaveProfile} className="space-y-6">
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-20 w-20">
                            <AvatarImage src="/placeholder.svg?height=80&width=80" alt="Taxhaa" />
                            <AvatarFallback className="bg-primary text-primary-content text-xl">TX</AvatarFallback>
                          </Avatar>
                          <div className="space-y-2">
                            <Button variant="outline" size="sm" className="border-base-300">
                              <Camera className="h-4 w-4 mr-2" />
                              Changer l'avatar
                            </Button>
                            <p className="text-sm text-base-content/60">
                              JPG, PNG ou GIF. Taille maximale : 2MB.
                            </p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="display-name">Nom d'affichage</Label>
                          <Input
                            id="display-name"
                            defaultValue="Taxhaa le Conquérant"
                            className="bg-base-200 border-base-300"
                          />
                          <p className="text-xs text-base-content/60">
                            Votre nom d'utilisateur Discord: <span className="font-medium">Taxhaa#1234</span>
                          </p>
                        </div>

                        <div className="space-y-2">
                          <Label>Compte Discord connecté</Label>
                          <div className="flex items-center space-x-3 p-3 bg-base-200/50 rounded-lg">
                            <MessageSquare className="h-5 w-5 text-[#5865F2]" />
                            <div className="flex-1">
                              <p className="font-medium">Taxhaa#1234</p>
                              <p className="text-sm text-base-content/60">Connecté via Discord OAuth</p>
                            </div>
                            <Badge className="bg-success/20 text-success border-success/30">
                              Connecté
                            </Badge>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="bio">Biographie</Label>
                          <Textarea
                            id="bio"
                            placeholder="Parlez-nous de vous..."
                            defaultValue="Guerrier légendaire du Royaume des Tempêtes, maître des arts martiaux et protecteur des terres mystiques."
                            className="bg-base-200 border-base-300 min-h-[100px]"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Rang actuel</Label>
                          <div className="flex items-center space-x-2">
                            <Crown className="h-5 w-5 text-warning" />
                            <Badge className="bg-warning/20 text-warning border-warning/30">
                              Seigneur de Guerre
                            </Badge>
                          </div>
                        </div>

                        <Button
                          type="submit"
                          className="bg-primary hover:bg-primary/90 text-primary-content"
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                              className="w-4 h-4 border-2 border-primary-content border-t-transparent rounded-full mr-2"
                            />
                          ) : (
                            <Save className="h-4 w-4 mr-2" />
                          )}
                          Sauvegarder les modifications
                        </Button>
                      </form>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="notifications" className="space-y-4">
                  <Card className="bg-base-100 border-base-300">
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Bell className="h-5 w-5 text-primary" />
                        <span>Préférences de notification</span>
                      </CardTitle>
                      <CardDescription>
                        Configurez comment vous souhaitez être notifié.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Notifications par email</Label>
                          <p className="text-sm text-base-content/60">
                            Recevoir des notifications importantes par email
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Notifications de guilde</Label>
                          <p className="text-sm text-base-content/60">
                            Être notifié des activités de votre guilde
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Notifications PvP</Label>
                          <p className="text-sm text-base-content/60">
                            Recevoir des alertes pour les événements PvP
                          </p>
                        </div>
                        <Switch />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Mises à jour du jeu</Label>
                          <p className="text-sm text-base-content/60">
                            Être informé des nouvelles fonctionnalités
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="preferences" className="space-y-4">
                  <Card className="bg-base-100 border-base-300">
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Palette className="h-5 w-5 text-primary" />
                        <span>Préférences d'affichage</span>
                      </CardTitle>
                      <CardDescription>
                        Personnalisez l'apparence de votre interface.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <Label htmlFor="language">Langue</Label>
                        <Select defaultValue="fr">
                          <SelectTrigger className="bg-base-200 border-base-300">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="fr">Français</SelectItem>
                            <SelectItem value="en">English</SelectItem>
                            <SelectItem value="es">Español</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="timezone">Fuseau horaire</Label>
                        <Select defaultValue="europe/paris">
                          <SelectTrigger className="bg-base-200 border-base-300">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="europe/paris">Europe/Paris (UTC+1)</SelectItem>
                            <SelectItem value="europe/london">Europe/London (UTC+0)</SelectItem>
                            <SelectItem value="america/new_york">America/New_York (UTC-5)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Mode compact</Label>
                          <p className="text-sm text-base-content/60">
                            Affichage plus dense de l'interface
                          </p>
                        </div>
                        <Switch />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Animations</Label>
                          <p className="text-sm text-base-content/60">
                            Activer les animations de l'interface
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </motion.div>
        </motion.div>
      </div>
    </>
  )
}