"use client"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Swords, Shield, Trophy, Crown, Timer, Users, Target, Coins } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { motion } from "framer-motion"

export default function PvpPage() {
  return (
    <div className="dark">
      <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>PvP</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="flex-1 space-y-6 p-6 min-h-screen relative overflow-hidden">
            {/* Full Screen Background Images */}
            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.04, scale: 1 }}
              transition={{ duration: 3, delay: 0.3 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image1.png" alt="" className="w-full h-full object-cover opacity-12" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.03, scale: 1 }}
              transition={{ duration: 3, delay: 0.8 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image2.png" alt="" className="w-full h-full object-cover opacity-10 mix-blend-overlay" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 0.05, scale: 1 }}
              transition={{ duration: 3, delay: 1.3 }}
              className="absolute inset-0 z-0"
            >
              <img src="/image3.png" alt="" className="w-full h-full object-cover opacity-15 mix-blend-soft-light" />
            </motion.div>

            {/* Modes de Jeu */}
            <Card className="bg-base-100/95 backdrop-blur border-red-500/20 shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Swords className="h-6 w-6 text-red-400" />
                  <div>
                    <CardTitle className="text-base-content">Modes de Combat</CardTitle>
                    <CardDescription className="text-base-content/70">Choisissez votre arène et prouvez votre valeur</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="relative p-4 rounded bg-red-500/10 border border-red-500/20 overflow-hidden">
                    <div className="relative z-10">
                      <Badge className="mb-2 bg-red-500/20 text-red-300">1v1</Badge>
                      <h3 className="font-semibold text-lg mb-2">Duel</h3>
                      <p className="text-sm text-muted-foreground mb-4">Affrontez un adversaire en combat singulier</p>
                      <div className="flex items-center gap-2 text-sm">
                        <Timer className="h-4 w-4 text-red-400" />
                        <span>3 minutes par match</span>
                      </div>
                    </div>
                  </div>
                  <div className="relative p-4 rounded bg-red-500/10 border border-red-500/20 overflow-hidden">
                    <div className="relative z-10">
                      <Badge className="mb-2 bg-orange-500/20 text-orange-300">3v3</Badge>
                      <h3 className="font-semibold text-lg mb-2">Arène</h3>
                      <p className="text-sm text-muted-foreground mb-4">Combat tactique en équipe de trois</p>
                      <div className="flex items-center gap-2 text-sm">
                        <Timer className="h-4 w-4 text-red-400" />
                        <span>5 minutes par match</span>
                      </div>
                    </div>
                  </div>
                  <div className="relative p-4 rounded bg-red-500/10 border border-red-500/20 overflow-hidden">
                    <div className="relative z-10">
                      <Badge className="mb-2 bg-yellow-500/20 text-yellow-300">5v5</Badge>
                      <h3 className="font-semibold text-lg mb-2">Champ de Bataille</h3>
                      <p className="text-sm text-muted-foreground mb-4">Guerre stratégique en équipe</p>
                      <div className="flex items-center gap-2 text-sm">
                        <Timer className="h-4 w-4 text-red-400" />
                        <span>10 minutes par match</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Saison en Cours */}
            <Card className="bg-base-100/95 backdrop-blur border-purple-500/20 shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Crown className="h-6 w-6 text-purple-400" />
                  <div>
                    <CardTitle className="text-base-content">Saison 1 - Les Tempêtes de Guerre</CardTitle>
                    <CardDescription className="text-base-content/70">Progression de la saison actuelle</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Progression de la Saison</span>
                      <span className="text-purple-400">45 jours restants</span>
                    </div>
                    <Progress value={60} className="h-2 bg-purple-500/10" />
                  </div>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="p-4 rounded bg-purple-500/10 border border-purple-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Target className="h-4 w-4 text-purple-400" />
                        <span className="font-semibold">Objectifs</span>
                      </div>
                      <p className="text-sm text-muted-foreground">Atteignez le rang Maître</p>
                    </div>
                    <div className="p-4 rounded bg-purple-500/10 border border-purple-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Trophy className="h-4 w-4 text-purple-400" />
                        <span className="font-semibold">Récompenses</span>
                      </div>
                      <p className="text-sm text-muted-foreground">Armure exclusive de saison</p>
                    </div>
                    <div className="p-4 rounded bg-purple-500/10 border border-purple-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="h-4 w-4 text-purple-400" />
                        <span className="font-semibold">Participants</span>
                      </div>
                      <p className="text-sm text-muted-foreground">1,245 joueurs classés</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Récompenses */}
            <Card className="bg-base-100/95 backdrop-blur border-yellow-500/20 shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Coins className="h-6 w-6 text-yellow-400" />
                  <div>
                    <CardTitle className="text-base-content">Récompenses PvP</CardTitle>
                    <CardDescription className="text-base-content/70">Gagnez des récompenses exclusives en combattant</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="p-4 rounded bg-yellow-500/10 border border-yellow-500/20">
                    <Badge className="mb-2 bg-bronze-500/20 text-orange-300">Bronze</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">100 points PvP</li>
                      <li className="text-foreground">Titre "Combattant"</li>
                      <li className="text-foreground">1,000 pièces d'or</li>
                    </ul>
                  </div>
                  <div className="p-4 rounded bg-yellow-500/10 border border-yellow-500/20">
                    <Badge className="mb-2 bg-slate-300/20 text-slate-300">Argent</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">500 points PvP</li>
                      <li className="text-foreground">Titre "Vétéran"</li>
                      <li className="text-foreground">5,000 pièces d'or</li>
                    </ul>
                  </div>
                  <div className="p-4 rounded bg-yellow-500/10 border border-yellow-500/20">
                    <Badge className="mb-2 bg-yellow-500/20 text-yellow-300">Or</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">1,000 points PvP</li>
                      <li className="text-foreground">Titre "Champion"</li>
                      <li className="text-foreground">10,000 pièces d'or</li>
                    </ul>
                  </div>
                  <div className="p-4 rounded bg-yellow-500/10 border border-yellow-500/20">
                    <Badge className="mb-2 bg-purple-500/20 text-purple-300">Maître</Badge>
                    <ul className="space-y-2 text-sm">
                      <li className="text-foreground">2,000 points PvP</li>
                      <li className="text-foreground">Titre "Légende"</li>
                      <li className="text-foreground">50,000 pièces d'or</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
      </main>
    </div>
  )
}